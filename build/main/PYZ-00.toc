('E:\\OBS\\build\\main\\PYZ-00.pyz',
 [('PyQt5',
   'e:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('__future__', 'D:\\python310\\lib\\__future__.py', 'PYMODULE'),
  ('_aix_support', 'D:\\python310\\lib\\_aix_support.py', 'PYMODULE'),
  ('_bootsubprocess', 'D:\\python310\\lib\\_bootsubprocess.py', 'PYMODULE'),
  ('_compat_pickle', 'D:\\python310\\lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'D:\\python310\\lib\\_compression.py', 'PYMODULE'),
  ('_py_abc', 'D:\\python310\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\python310\\lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyi_rth_utils',
   'e:\\OBS\\.venv\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'e:\\OBS\\.venv\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_soundfile',
   'e:\\OBS\\.venv\\lib\\site-packages\\_soundfile.py',
   'PYMODULE'),
  ('_soundfile_data',
   'e:\\OBS\\.venv\\lib\\site-packages\\_soundfile_data\\__init__.py',
   'PYMODULE'),
  ('_strptime', 'D:\\python310\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'D:\\python310\\lib\\_threading_local.py', 'PYMODULE'),
  ('argparse', 'D:\\python310\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\python310\\lib\\ast.py', 'PYMODULE'),
  ('asyncio', 'D:\\python310\\lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'D:\\python310\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\python310\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\python310\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\python310\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\python310\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\python310\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events', 'D:\\python310\\lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\python310\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\python310\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures', 'D:\\python310\\lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.locks', 'D:\\python310\\lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.log', 'D:\\python310\\lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.mixins', 'D:\\python310\\lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\python310\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\python310\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues', 'D:\\python310\\lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners', 'D:\\python310\\lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\python310\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto', 'D:\\python310\\lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.staggered',
   'D:\\python310\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams', 'D:\\python310\\lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\python310\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks', 'D:\\python310\\lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.threads', 'D:\\python310\\lib\\asyncio\\threads.py', 'PYMODULE'),
  ('asyncio.transports',
   'D:\\python310\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock', 'D:\\python310\\lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\python310\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\python310\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\python310\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64', 'D:\\python310\\lib\\base64.py', 'PYMODULE'),
  ('bdb', 'D:\\python310\\lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'D:\\python310\\lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\python310\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\python310\\lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'e:\\OBS\\.venv\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'e:\\OBS\\.venv\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('charset_normalizer',
   'e:\\OBS\\.venv\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'e:\\OBS\\.venv\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'e:\\OBS\\.venv\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'e:\\OBS\\.venv\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'e:\\OBS\\.venv\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'e:\\OBS\\.venv\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'e:\\OBS\\.venv\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'e:\\OBS\\.venv\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('cmd', 'D:\\python310\\lib\\cmd.py', 'PYMODULE'),
  ('code', 'D:\\python310\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\python310\\lib\\codeop.py', 'PYMODULE'),
  ('commctrl',
   'e:\\OBS\\.venv\\lib\\site-packages\\win32\\lib\\commctrl.py',
   'PYMODULE'),
  ('concurrent', 'D:\\python310\\lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('concurrent.futures',
   'D:\\python310\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\python310\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\python310\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\python310\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('contextlib', 'D:\\python310\\lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'D:\\python310\\lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'D:\\python310\\lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\python310\\lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'D:\\python310\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix', 'D:\\python310\\lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes._endian', 'D:\\python310\\lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('ctypes.macholib',
   'D:\\python310\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\python310\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\python310\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\python310\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util', 'D:\\python310\\lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes.wintypes', 'D:\\python310\\lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('dataclasses', 'D:\\python310\\lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'D:\\python310\\lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'D:\\python310\\lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'D:\\python310\\lib\\difflib.py', 'PYMODULE'),
  ('dis', 'D:\\python310\\lib\\dis.py', 'PYMODULE'),
  ('doctest', 'D:\\python310\\lib\\doctest.py', 'PYMODULE'),
  ('dotenv',
   'e:\\OBS\\.venv\\lib\\site-packages\\dotenv\\__init__.py',
   'PYMODULE'),
  ('dotenv.ipython',
   'e:\\OBS\\.venv\\lib\\site-packages\\dotenv\\ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   'e:\\OBS\\.venv\\lib\\site-packages\\dotenv\\main.py',
   'PYMODULE'),
  ('dotenv.parser',
   'e:\\OBS\\.venv\\lib\\site-packages\\dotenv\\parser.py',
   'PYMODULE'),
  ('dotenv.variables',
   'e:\\OBS\\.venv\\lib\\site-packages\\dotenv\\variables.py',
   'PYMODULE'),
  ('email', 'D:\\python310\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\python310\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\python310\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'D:\\python310\\lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase',
   'D:\\python310\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime', 'D:\\python310\\lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'D:\\python310\\lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\python310\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'D:\\python310\\lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'D:\\python310\\lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'D:\\python310\\lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'D:\\python310\\lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'D:\\python310\\lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\python310\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'D:\\python310\\lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'D:\\python310\\lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'D:\\python310\\lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'D:\\python310\\lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'D:\\python310\\lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'D:\\python310\\lib\\email\\utils.py', 'PYMODULE'),
  ('fileinput', 'D:\\python310\\lib\\fileinput.py', 'PYMODULE'),
  ('fnmatch', 'D:\\python310\\lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\python310\\lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'D:\\python310\\lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\python310\\lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\python310\\lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\python310\\lib\\gettext.py', 'PYMODULE'),
  ('glob', 'D:\\python310\\lib\\glob.py', 'PYMODULE'),
  ('gzip', 'D:\\python310\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\python310\\lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\python310\\lib\\hmac.py', 'PYMODULE'),
  ('html', 'D:\\python310\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'D:\\python310\\lib\\html\\entities.py', 'PYMODULE'),
  ('http', 'D:\\python310\\lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'D:\\python310\\lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'D:\\python310\\lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http.cookies', 'D:\\python310\\lib\\http\\cookies.py', 'PYMODULE'),
  ('http.server', 'D:\\python310\\lib\\http\\server.py', 'PYMODULE'),
  ('idna', 'e:\\OBS\\.venv\\lib\\site-packages\\idna\\__init__.py', 'PYMODULE'),
  ('idna.core',
   'e:\\OBS\\.venv\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'e:\\OBS\\.venv\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'e:\\OBS\\.venv\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'e:\\OBS\\.venv\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'e:\\OBS\\.venv\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib', 'D:\\python310\\lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc', 'D:\\python310\\lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._adapters',
   'D:\\python310\\lib\\importlib\\_adapters.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\python310\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\python310\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib._common',
   'D:\\python310\\lib\\importlib\\_common.py',
   'PYMODULE'),
  ('importlib.abc', 'D:\\python310\\lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\python310\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\python310\\lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\python310\\lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\python310\\lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\python310\\lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\python310\\lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\python310\\lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\python310\\lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\python310\\lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\python310\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.util', 'D:\\python310\\lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'D:\\python310\\lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\python310\\lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'D:\\python310\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'D:\\python310\\lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'D:\\python310\\lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\python310\\lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'D:\\python310\\lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'D:\\python310\\lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'D:\\python310\\lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\python310\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\python310\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\python310\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\python310\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\python310\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\python310\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\python310\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\python310\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\python310\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\python310\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\python310\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\python310\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\python310\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\python310\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\python310\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\python310\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\python310\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\python310\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\python310\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\python310\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\python310\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\python310\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\python310\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'D:\\python310\\lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'D:\\python310\\lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'D:\\python310\\lib\\numbers.py', 'PYMODULE'),
  ('numpy',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('numpy._core.tests._natype',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'D:\\python310\\lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'D:\\python310\\lib\\optparse.py', 'PYMODULE'),
  ('pathlib', 'D:\\python310\\lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'D:\\python310\\lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'D:\\python310\\lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'D:\\python310\\lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'D:\\python310\\lib\\platform.py', 'PYMODULE'),
  ('pprint', 'D:\\python310\\lib\\pprint.py', 'PYMODULE'),
  ('psutil',
   'e:\\OBS\\.venv\\lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'e:\\OBS\\.venv\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'e:\\OBS\\.venv\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('py_compile', 'D:\\python310\\lib\\py_compile.py', 'PYMODULE'),
  ('pydoc', 'D:\\python310\\lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data', 'D:\\python310\\lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\python310\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pythoncom', 'e:\\OBS\\.venv\\lib\\site-packages\\pythoncom.py', 'PYMODULE'),
  ('pywin',
   'e:\\OBS\\.venv\\lib\\site-packages\\Pythonwin\\pywin\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs',
   'e:\\OBS\\.venv\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs.list',
   'e:\\OBS\\.venv\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\list.py',
   'PYMODULE'),
  ('pywin.dialogs.status',
   'e:\\OBS\\.venv\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\status.py',
   'PYMODULE'),
  ('pywin.mfc',
   'e:\\OBS\\.venv\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\__init__.py',
   'PYMODULE'),
  ('pywin.mfc.dialog',
   'e:\\OBS\\.venv\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\dialog.py',
   'PYMODULE'),
  ('pywin.mfc.object',
   'e:\\OBS\\.venv\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\object.py',
   'PYMODULE'),
  ('pywin.mfc.thread',
   'e:\\OBS\\.venv\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\thread.py',
   'PYMODULE'),
  ('pywin.mfc.window',
   'e:\\OBS\\.venv\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\window.py',
   'PYMODULE'),
  ('pywin32_system32', '-', 'PYMODULE'),
  ('pywintypes',
   'e:\\OBS\\.venv\\lib\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE'),
  ('queue', 'D:\\python310\\lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\python310\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\python310\\lib\\random.py', 'PYMODULE'),
  ('requests',
   'e:\\OBS\\.venv\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'e:\\OBS\\.venv\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'e:\\OBS\\.venv\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'e:\\OBS\\.venv\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'e:\\OBS\\.venv\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'e:\\OBS\\.venv\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'e:\\OBS\\.venv\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'e:\\OBS\\.venv\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'e:\\OBS\\.venv\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'e:\\OBS\\.venv\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'e:\\OBS\\.venv\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'e:\\OBS\\.venv\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'e:\\OBS\\.venv\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'e:\\OBS\\.venv\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'e:\\OBS\\.venv\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'e:\\OBS\\.venv\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'e:\\OBS\\.venv\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('runpy', 'D:\\python310\\lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'D:\\python310\\lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'D:\\python310\\lib\\selectors.py', 'PYMODULE'),
  ('shlex', 'D:\\python310\\lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\python310\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\python310\\lib\\signal.py', 'PYMODULE'),
  ('socket', 'D:\\python310\\lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'D:\\python310\\lib\\socketserver.py', 'PYMODULE'),
  ('soundfile', 'e:\\OBS\\.venv\\lib\\site-packages\\soundfile.py', 'PYMODULE'),
  ('ssl', 'D:\\python310\\lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'D:\\python310\\lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\python310\\lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\python310\\lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\python310\\lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'D:\\python310\\lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'D:\\python310\\lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\python310\\lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\python310\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\python310\\lib\\threading.py', 'PYMODULE'),
  ('threadpoolctl',
   'e:\\OBS\\.venv\\lib\\site-packages\\threadpoolctl.py',
   'PYMODULE'),
  ('token', 'D:\\python310\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\python310\\lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\python310\\lib\\tracemalloc.py', 'PYMODULE'),
  ('tty', 'D:\\python310\\lib\\tty.py', 'PYMODULE'),
  ('typing', 'D:\\python310\\lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'e:\\OBS\\.venv\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest', 'D:\\python310\\lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest._log', 'D:\\python310\\lib\\unittest\\_log.py', 'PYMODULE'),
  ('unittest.async_case',
   'D:\\python310\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case', 'D:\\python310\\lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest.loader', 'D:\\python310\\lib\\unittest\\loader.py', 'PYMODULE'),
  ('unittest.main', 'D:\\python310\\lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.result', 'D:\\python310\\lib\\unittest\\result.py', 'PYMODULE'),
  ('unittest.runner', 'D:\\python310\\lib\\unittest\\runner.py', 'PYMODULE'),
  ('unittest.signals', 'D:\\python310\\lib\\unittest\\signals.py', 'PYMODULE'),
  ('unittest.suite', 'D:\\python310\\lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.util', 'D:\\python310\\lib\\unittest\\util.py', 'PYMODULE'),
  ('urllib', 'D:\\python310\\lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'D:\\python310\\lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\python310\\lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request', 'D:\\python310\\lib\\urllib\\request.py', 'PYMODULE'),
  ('urllib.response', 'D:\\python310\\lib\\urllib\\response.py', 'PYMODULE'),
  ('urllib3',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'e:\\OBS\\.venv\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uu', 'D:\\python310\\lib\\uu.py', 'PYMODULE'),
  ('uuid', 'D:\\python310\\lib\\uuid.py', 'PYMODULE'),
  ('webbrowser', 'D:\\python310\\lib\\webbrowser.py', 'PYMODULE'),
  ('websocket',
   'e:\\OBS\\.venv\\lib\\site-packages\\websocket\\__init__.py',
   'PYMODULE'),
  ('websocket._abnf',
   'e:\\OBS\\.venv\\lib\\site-packages\\websocket\\_abnf.py',
   'PYMODULE'),
  ('websocket._app',
   'e:\\OBS\\.venv\\lib\\site-packages\\websocket\\_app.py',
   'PYMODULE'),
  ('websocket._cookiejar',
   'e:\\OBS\\.venv\\lib\\site-packages\\websocket\\_cookiejar.py',
   'PYMODULE'),
  ('websocket._core',
   'e:\\OBS\\.venv\\lib\\site-packages\\websocket\\_core.py',
   'PYMODULE'),
  ('websocket._exceptions',
   'e:\\OBS\\.venv\\lib\\site-packages\\websocket\\_exceptions.py',
   'PYMODULE'),
  ('websocket._handshake',
   'e:\\OBS\\.venv\\lib\\site-packages\\websocket\\_handshake.py',
   'PYMODULE'),
  ('websocket._http',
   'e:\\OBS\\.venv\\lib\\site-packages\\websocket\\_http.py',
   'PYMODULE'),
  ('websocket._logging',
   'e:\\OBS\\.venv\\lib\\site-packages\\websocket\\_logging.py',
   'PYMODULE'),
  ('websocket._socket',
   'e:\\OBS\\.venv\\lib\\site-packages\\websocket\\_socket.py',
   'PYMODULE'),
  ('websocket._ssl_compat',
   'e:\\OBS\\.venv\\lib\\site-packages\\websocket\\_ssl_compat.py',
   'PYMODULE'),
  ('websocket._url',
   'e:\\OBS\\.venv\\lib\\site-packages\\websocket\\_url.py',
   'PYMODULE'),
  ('websocket._utils',
   'e:\\OBS\\.venv\\lib\\site-packages\\websocket\\_utils.py',
   'PYMODULE'),
  ('websockets',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\__init__.py',
   'PYMODULE'),
  ('websockets.__main__',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\__main__.py',
   'PYMODULE'),
  ('websockets.asyncio',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\asyncio\\__init__.py',
   'PYMODULE'),
  ('websockets.asyncio.async_timeout',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\asyncio\\async_timeout.py',
   'PYMODULE'),
  ('websockets.asyncio.client',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\asyncio\\client.py',
   'PYMODULE'),
  ('websockets.asyncio.compatibility',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\asyncio\\compatibility.py',
   'PYMODULE'),
  ('websockets.asyncio.connection',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\asyncio\\connection.py',
   'PYMODULE'),
  ('websockets.asyncio.messages',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\asyncio\\messages.py',
   'PYMODULE'),
  ('websockets.asyncio.router',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\asyncio\\router.py',
   'PYMODULE'),
  ('websockets.asyncio.server',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\asyncio\\server.py',
   'PYMODULE'),
  ('websockets.auth',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\auth.py',
   'PYMODULE'),
  ('websockets.cli',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\cli.py',
   'PYMODULE'),
  ('websockets.client',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\client.py',
   'PYMODULE'),
  ('websockets.connection',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\connection.py',
   'PYMODULE'),
  ('websockets.datastructures',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\datastructures.py',
   'PYMODULE'),
  ('websockets.exceptions',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\exceptions.py',
   'PYMODULE'),
  ('websockets.extensions',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\extensions\\__init__.py',
   'PYMODULE'),
  ('websockets.extensions.base',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\extensions\\base.py',
   'PYMODULE'),
  ('websockets.extensions.permessage_deflate',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\extensions\\permessage_deflate.py',
   'PYMODULE'),
  ('websockets.frames',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\frames.py',
   'PYMODULE'),
  ('websockets.headers',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\headers.py',
   'PYMODULE'),
  ('websockets.http',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\http.py',
   'PYMODULE'),
  ('websockets.http11',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\http11.py',
   'PYMODULE'),
  ('websockets.imports',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\imports.py',
   'PYMODULE'),
  ('websockets.legacy',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\legacy\\__init__.py',
   'PYMODULE'),
  ('websockets.legacy.auth',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\legacy\\auth.py',
   'PYMODULE'),
  ('websockets.legacy.client',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\legacy\\client.py',
   'PYMODULE'),
  ('websockets.legacy.exceptions',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\legacy\\exceptions.py',
   'PYMODULE'),
  ('websockets.legacy.framing',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\legacy\\framing.py',
   'PYMODULE'),
  ('websockets.legacy.handshake',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\legacy\\handshake.py',
   'PYMODULE'),
  ('websockets.legacy.http',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\legacy\\http.py',
   'PYMODULE'),
  ('websockets.legacy.protocol',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\legacy\\protocol.py',
   'PYMODULE'),
  ('websockets.legacy.server',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\legacy\\server.py',
   'PYMODULE'),
  ('websockets.protocol',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\protocol.py',
   'PYMODULE'),
  ('websockets.server',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\server.py',
   'PYMODULE'),
  ('websockets.streams',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\streams.py',
   'PYMODULE'),
  ('websockets.sync',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\sync\\__init__.py',
   'PYMODULE'),
  ('websockets.sync.client',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\sync\\client.py',
   'PYMODULE'),
  ('websockets.sync.connection',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\sync\\connection.py',
   'PYMODULE'),
  ('websockets.sync.messages',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\sync\\messages.py',
   'PYMODULE'),
  ('websockets.sync.router',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\sync\\router.py',
   'PYMODULE'),
  ('websockets.sync.server',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\sync\\server.py',
   'PYMODULE'),
  ('websockets.sync.utils',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\sync\\utils.py',
   'PYMODULE'),
  ('websockets.typing',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\typing.py',
   'PYMODULE'),
  ('websockets.uri',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\uri.py',
   'PYMODULE'),
  ('websockets.utils',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\utils.py',
   'PYMODULE'),
  ('websockets.version',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\version.py',
   'PYMODULE'),
  ('win32com',
   'e:\\OBS\\.venv\\lib\\site-packages\\win32com\\__init__.py',
   'PYMODULE'),
  ('win32com.client',
   'e:\\OBS\\.venv\\lib\\site-packages\\win32com\\client\\__init__.py',
   'PYMODULE'),
  ('win32com.client.CLSIDToClass',
   'e:\\OBS\\.venv\\lib\\site-packages\\win32com\\client\\CLSIDToClass.py',
   'PYMODULE'),
  ('win32com.client.build',
   'e:\\OBS\\.venv\\lib\\site-packages\\win32com\\client\\build.py',
   'PYMODULE'),
  ('win32com.client.dynamic',
   'e:\\OBS\\.venv\\lib\\site-packages\\win32com\\client\\dynamic.py',
   'PYMODULE'),
  ('win32com.client.gencache',
   'e:\\OBS\\.venv\\lib\\site-packages\\win32com\\client\\gencache.py',
   'PYMODULE'),
  ('win32com.client.genpy',
   'e:\\OBS\\.venv\\lib\\site-packages\\win32com\\client\\genpy.py',
   'PYMODULE'),
  ('win32com.client.makepy',
   'e:\\OBS\\.venv\\lib\\site-packages\\win32com\\client\\makepy.py',
   'PYMODULE'),
  ('win32com.client.selecttlb',
   'e:\\OBS\\.venv\\lib\\site-packages\\win32com\\client\\selecttlb.py',
   'PYMODULE'),
  ('win32com.client.util',
   'e:\\OBS\\.venv\\lib\\site-packages\\win32com\\client\\util.py',
   'PYMODULE'),
  ('win32com.server',
   'e:\\OBS\\.venv\\lib\\site-packages\\win32com\\server\\__init__.py',
   'PYMODULE'),
  ('win32com.server.dispatcher',
   'e:\\OBS\\.venv\\lib\\site-packages\\win32com\\server\\dispatcher.py',
   'PYMODULE'),
  ('win32com.server.exception',
   'e:\\OBS\\.venv\\lib\\site-packages\\win32com\\server\\exception.py',
   'PYMODULE'),
  ('win32com.server.policy',
   'e:\\OBS\\.venv\\lib\\site-packages\\win32com\\server\\policy.py',
   'PYMODULE'),
  ('win32com.server.util',
   'e:\\OBS\\.venv\\lib\\site-packages\\win32com\\server\\util.py',
   'PYMODULE'),
  ('win32com.universal',
   'e:\\OBS\\.venv\\lib\\site-packages\\win32com\\universal.py',
   'PYMODULE'),
  ('win32com.util',
   'e:\\OBS\\.venv\\lib\\site-packages\\win32com\\util.py',
   'PYMODULE'),
  ('win32con',
   'e:\\OBS\\.venv\\lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32traceutil',
   'e:\\OBS\\.venv\\lib\\site-packages\\win32\\lib\\win32traceutil.py',
   'PYMODULE'),
  ('winerror',
   'e:\\OBS\\.venv\\lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('wmi', 'e:\\OBS\\.venv\\lib\\site-packages\\wmi.py', 'PYMODULE'),
  ('xml', 'D:\\python310\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.parsers', 'D:\\python310\\lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\python310\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax', 'D:\\python310\\lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\python310\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\python310\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler', 'D:\\python310\\lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax.saxutils', 'D:\\python310\\lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\python310\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc', 'D:\\python310\\lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client', 'D:\\python310\\lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('zipfile', 'D:\\python310\\lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'D:\\python310\\lib\\zipimport.py', 'PYMODULE')])
