('E:\\OBS\\build\\main\\main.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz', 'E:\\OBS\\build\\main\\PYZ-00.pyz', 'PYZ'),
  ('struct', 'E:\\OBS\\build\\main\\localpycs\\struct.pyc', 'PYMODULE'),
  ('pyimod01_archive',
   'E:\\OBS\\build\\main\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'E:\\OBS\\build\\main\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'E:\\OBS\\build\\main\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'E:\\OBS\\build\\main\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'e:\\OBS\\.venv\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'E:\\OBS\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'E:\\OBS\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'E:\\OBS\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pywintypes',
   'E:\\OBS\\.venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pywintypes.py',
   'PYSOURCE'),
  ('pyi_rth_pythoncom',
   'E:\\OBS\\.venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pythoncom.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'E:\\OBS\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('main', 'E:\\OBS\\main.py', 'PYSOURCE'),
  ('python310.dll', 'D:\\python310\\python310.dll', 'BINARY'),
  ('_soundfile_data\\libsndfile_x64.dll',
   'e:\\OBS\\.venv\\lib\\site-packages\\_soundfile_data\\libsndfile_x64.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes310.dll',
   'e:\\OBS\\.venv\\lib\\site-packages\\pywin32_system32\\pywintypes310.dll',
   'BINARY'),
  ('pywin32_system32\\pythoncom310.dll',
   'e:\\OBS\\.venv\\lib\\site-packages\\pywin32_system32\\pythoncom310.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\audio\\qtaudio_windows.dll',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\audio\\qtaudio_windows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\mediaservice\\wmfengine.dll',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\mediaservice\\wmfengine.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\playlistformats\\qtmultimedia_m3u.dll',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\playlistformats\\qtmultimedia_m3u.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\mediaservice\\qtmedia_audioengine.dll',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\mediaservice\\qtmedia_audioengine.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\audio\\qtaudio_wasapi.dll',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\audio\\qtaudio_wasapi.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\mediaservice\\dsengine.dll',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\mediaservice\\dsengine.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libcrypto-1_1-x64.dll',
   'E:\\OBS\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libcrypto-1_1-x64.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libssl-1_1-x64.dll',
   'E:\\OBS\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libssl-1_1-x64.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\bearer\\qgenericbearer.dll',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\bearer\\qgenericbearer.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'E:\\OBS\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'E:\\OBS\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'E:\\OBS\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'E:\\OBS\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('unicodedata.pyd', 'D:\\python310\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'D:\\python310\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('select.pyd', 'D:\\python310\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\python310\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\python310\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\python310\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\python310\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'D:\\python310\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'D:\\python310\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_queue.pyd', 'D:\\python310\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'D:\\python310\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_cffi_backend.cp310-win_amd64.pyd',
   'e:\\OBS\\.venv\\lib\\site-packages\\_cffi_backend.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_ctypes.pyd', 'D:\\python310\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp310-win_amd64.pyd',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp310-win_amd64.pyd',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp310-win_amd64.pyd',
   'e:\\OBS\\.venv\\lib\\site-packages\\charset_normalizer\\md__mypyc.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp310-win_amd64.pyd',
   'e:\\OBS\\.venv\\lib\\site-packages\\charset_normalizer\\md.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'e:\\OBS\\.venv\\lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp310-win_amd64.pyd',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp310-win_amd64.pyd',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\random\\mtrand.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp310-win_amd64.pyd',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\random\\_sfc64.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp310-win_amd64.pyd',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\random\\_philox.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp310-win_amd64.pyd',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\random\\_pcg64.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp310-win_amd64.pyd',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\random\\_mt19937.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp310-win_amd64.pyd',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\random\\bit_generator.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp310-win_amd64.pyd',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\random\\_generator.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp310-win_amd64.pyd',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp310-win_amd64.pyd',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\random\\_common.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp310-win_amd64.pyd',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd', 'D:\\python310\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'D:\\python310\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('_uuid.pyd', 'D:\\python310\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('websockets\\speedups.cp310-win_amd64.pyd',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets\\speedups.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32security.pyd',
   'e:\\OBS\\.venv\\lib\\site-packages\\win32\\win32security.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'e:\\OBS\\.venv\\lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('win32\\win32process.pyd',
   'e:\\OBS\\.venv\\lib\\site-packages\\win32\\win32process.pyd',
   'EXTENSION'),
  ('win32\\win32gui.pyd',
   'e:\\OBS\\.venv\\lib\\site-packages\\win32\\win32gui.pyd',
   'EXTENSION'),
  ('win32\\_win32sysloader.pyd',
   'e:\\OBS\\.venv\\lib\\site-packages\\win32\\_win32sysloader.pyd',
   'EXTENSION'),
  ('Pythonwin\\win32ui.pyd',
   'e:\\OBS\\.venv\\lib\\site-packages\\Pythonwin\\win32ui.pyd',
   'EXTENSION'),
  ('win32\\win32event.pyd',
   'e:\\OBS\\.venv\\lib\\site-packages\\win32\\win32event.pyd',
   'EXTENSION'),
  ('win32\\win32trace.pyd',
   'e:\\OBS\\.venv\\lib\\site-packages\\win32\\win32trace.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'e:\\OBS\\.venv\\lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('main_module.cp310-win_amd64.pyd',
   'E:\\OBS\\main_module.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtMultimedia.pyd',
   'e:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\QtMultimedia.pyd',
   'EXTENSION'),
  ('PyQt5\\QtNetwork.pyd',
   'e:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\QtNetwork.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp310-win_amd64.pyd',
   'e:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\sip.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'e:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'e:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'e:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll', 'D:\\python310\\VCRUNTIME140.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll', 'D:\\python310\\VCRUNTIME140_1.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'e:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Multimedia.dll',
   'e:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Multimedia.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'e:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'e:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'e:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'e:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'E:\\OBS\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'E:\\OBS\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'e:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'e:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'e:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'e:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('libcrypto-1_1.dll', 'D:\\python310\\DLLs\\libcrypto-1_1.dll', 'BINARY'),
  ('libssl-1_1.dll', 'D:\\python310\\DLLs\\libssl-1_1.dll', 'BINARY'),
  ('libffi-7.dll', 'D:\\python310\\DLLs\\libffi-7.dll', 'BINARY'),
  ('Pythonwin\\mfc140u.dll',
   'e:\\OBS\\.venv\\lib\\site-packages\\Pythonwin\\mfc140u.dll',
   'BINARY'),
  ('python3.dll', 'D:\\python310\\python3.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'e:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'e:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'e:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('obs2.ico', 'E:\\OBS\\obs2.ico', 'DATA'),
  ('zuozhe.png', 'E:\\OBS\\zuozhe.png', 'DATA'),
  ('_soundfile_data\\COPYING',
   'e:\\OBS\\.venv\\lib\\site-packages\\_soundfile_data\\COPYING',
   'DATA'),
  ('certifi\\cacert.pem',
   'e:\\OBS\\.venv\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'e:\\OBS\\.venv\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_ca.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_tr.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_uk.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_zh_TW.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_fi.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_hu.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_sk.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_en.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_ru.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_pl.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_de.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_ja.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_ar.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_it.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_bg.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_da.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_cs.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_fr.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_es.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_ko.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'E:\\OBS\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('numpy-2.2.6.dist-info\\REQUESTED',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy-2.2.6.dist-info\\REQUESTED',
   'DATA'),
  ('numpy-2.2.6.dist-info\\DELVEWHEEL',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy-2.2.6.dist-info\\DELVEWHEEL',
   'DATA'),
  ('websockets-15.0.1.dist-info\\RECORD',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets-15.0.1.dist-info\\RECORD',
   'DATA'),
  ('websockets-15.0.1.dist-info\\WHEEL',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets-15.0.1.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.2.6.dist-info\\RECORD',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy-2.2.6.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.2.6.dist-info\\INSTALLER',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy-2.2.6.dist-info\\INSTALLER',
   'DATA'),
  ('websockets-15.0.1.dist-info\\INSTALLER',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets-15.0.1.dist-info\\INSTALLER',
   'DATA'),
  ('websockets-15.0.1.dist-info\\LICENSE',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets-15.0.1.dist-info\\LICENSE',
   'DATA'),
  ('numpy-2.2.6.dist-info\\entry_points.txt',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy-2.2.6.dist-info\\entry_points.txt',
   'DATA'),
  ('websockets-15.0.1.dist-info\\REQUESTED',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets-15.0.1.dist-info\\REQUESTED',
   'DATA'),
  ('numpy-2.2.6.dist-info\\WHEEL',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy-2.2.6.dist-info\\WHEEL',
   'DATA'),
  ('websockets-15.0.1.dist-info\\METADATA',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets-15.0.1.dist-info\\METADATA',
   'DATA'),
  ('websockets-15.0.1.dist-info\\top_level.txt',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets-15.0.1.dist-info\\top_level.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\METADATA',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy-2.2.6.dist-info\\METADATA',
   'DATA'),
  ('websockets-15.0.1.dist-info\\entry_points.txt',
   'e:\\OBS\\.venv\\lib\\site-packages\\websockets-15.0.1.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\LICENSE.txt',
   'e:\\OBS\\.venv\\lib\\site-packages\\numpy-2.2.6.dist-info\\LICENSE.txt',
   'DATA'),
  ('base_library.zip', 'E:\\OBS\\build\\main\\base_library.zip', 'DATA')],
 'python310.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
